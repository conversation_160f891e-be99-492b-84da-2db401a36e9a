import React, { useState, useRef, useCallback } from 'react';
import { gsap } from 'gsap';

// 🎬 CONTRÔLEUR PRINCIPAL DE L'EXPÉRIENCE CINÉMATOGRAPHIQUE
// Système simplifié : UN BOUTON → VOLETS → CYCLE AUTO → FERMETURE

export type CinemaPhase = 'aube' | 'midi' | 'coucher' | 'nuit';
export type CinemaState = 'idle' | 'opening' | 'running' | 'closing' | 'complete';

interface CinemaControllerProps {
  children: React.ReactNode;
  onPhaseChange?: (phase: CinemaPhase) => void;
  onStateChange?: (state: CinemaState) => void;
  onComplete?: () => void;
}

// 🌅☀️🌇🌙 POSITIONS ASTRONOMIQUES EXACTES (CISCO CRITICAL)
const ASTRONOMICAL_POSITIONS = {
  aube: { 
    soleil: { start: -40, end: -10, opacity: [0, 0.3] },
    lune: { opacity: [0.3, 0] },
    etoiles: { density: ['high', 'medium'] },
    duration: 120000 // 2 minutes
  },
  midi: { 
    soleil: { start: -10, end: 90, opacity: [0.3, 1] }, // ZÉNITH ABSOLU
    lune: { opacity: [0, 0] }, // INVISIBLE COMPLET
    etoiles: { density: ['medium', 'none'] },
    duration: 120000 // 2 minutes
  },
  coucher: { 
    soleil: { start: 90, end: 170, opacity: [1, 0.3] },
    lune: { opacity: [0, 0.3] }, // APPARITION PROGRESSIVE
    etoiles: { density: ['none', 'medium'] },
    duration: 120000 // 2 minutes
  },
  nuit: { 
    soleil: { start: 170, end: 200, opacity: [0.3, 0] },
    lune: { opacity: [0.3, 1] }, // VISIBLE COMPLET
    etoiles: { density: ['medium', 'high'] },
    duration: 120000 // 2 minutes
  }
};

const CinemaController: React.FC<CinemaControllerProps> = ({
  children,
  onPhaseChange,
  onStateChange,
  onComplete
}) => {
  // 🎬 États du système cinématographique
  const [currentState, setCurrentState] = useState<CinemaState>('idle');
  const [currentPhase, setCurrentPhase] = useState<CinemaPhase>('aube');
  const [isExperienceRunning, setIsExperienceRunning] = useState(false);

  // 🎭 Références pour les volets cinématographiques
  const leftCurtainRef = useRef<HTMLDivElement>(null);
  const rightCurtainRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // ⏱️ Références pour les timers
  const phaseTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentPhaseIndexRef = useRef(0);

  // 🎬 SÉQUENCE DES PHASES AUTOMATIQUES
  const phases: CinemaPhase[] = ['aube', 'midi', 'coucher', 'nuit'];

  // 🔄 Changement d'état avec callbacks
  const updateState = useCallback((newState: CinemaState) => {
    console.log(`🎬 CinemaController: État ${currentState} → ${newState}`);
    setCurrentState(newState);
    onStateChange?.(newState);
  }, [currentState, onStateChange]);

  // 🔄 Changement de phase avec callbacks
  const updatePhase = useCallback((newPhase: CinemaPhase) => {
    console.log(`🌅 CinemaController: Phase ${currentPhase} → ${newPhase}`);
    setCurrentPhase(newPhase);
    onPhaseChange?.(newPhase);
  }, [currentPhase, onPhaseChange]);

  // 🎭 OUVERTURE DES VOLETS (effet cinématographique)
  const openCurtains = useCallback(() => {
    console.log('🎬 Ouverture des volets cinématographiques...');
    updateState('opening');

    const timeline = gsap.timeline({
      onComplete: () => {
        console.log('✅ Volets ouverts, démarrage du cycle automatique');
        startAutoCycle();
      }
    });

    // Animation des volets qui glissent vers les côtés
    timeline
      .to(leftCurtainRef.current, {
        x: '-100%',
        duration: 2,
        ease: 'power2.inOut'
      }, 0)
      .to(rightCurtainRef.current, {
        x: '100%',
        duration: 2,
        ease: 'power2.inOut'
      }, 0);

  }, [updateState]);

  // 🎭 FERMETURE DES VOLETS
  const closeCurtains = useCallback(() => {
    console.log('🎬 Fermeture des volets cinématographiques...');
    updateState('closing');

    const timeline = gsap.timeline({
      onComplete: () => {
        console.log('✅ Expérience cinématographique terminée');
        updateState('complete');
        setIsExperienceRunning(false);
        onComplete?.();
      }
    });

    // Retour des volets à leur position initiale
    timeline
      .to(leftCurtainRef.current, {
        x: '0%',
        duration: 2,
        ease: 'power2.inOut'
      }, 0)
      .to(rightCurtainRef.current, {
        x: '0%',
        duration: 2,
        ease: 'power2.inOut'
      }, 0);

  }, [updateState, onComplete]);

  // 🌅 DÉMARRAGE DU CYCLE AUTOMATIQUE
  const startAutoCycle = useCallback(() => {
    console.log('🌅 Démarrage du cycle automatique 4 phases (8 minutes total)');
    updateState('running');
    currentPhaseIndexRef.current = 0;
    
    // Démarrer par la première phase
    executePhase(0);
  }, [updateState]);

  // 🔄 EXÉCUTION D'UNE PHASE SPÉCIFIQUE
  const executePhase = useCallback((phaseIndex: number) => {
    if (phaseIndex >= phases.length) {
      // Cycle terminé, fermer les volets
      console.log('🎬 Cycle automatique terminé, fermeture des volets');
      closeCurtains();
      return;
    }

    const phase = phases[phaseIndex];
    const phaseConfig = ASTRONOMICAL_POSITIONS[phase];
    
    console.log(`🌅 Exécution phase ${phaseIndex + 1}/4: ${phase} (${phaseConfig.duration/1000}s)`);
    updatePhase(phase);

    // Timer pour passer à la phase suivante
    phaseTimerRef.current = setTimeout(() => {
      executePhase(phaseIndex + 1);
    }, phaseConfig.duration);

  }, [phases, updatePhase, closeCurtains]);

  // 🎮 DÉMARRAGE DE L'EXPÉRIENCE COMPLÈTE
  const startExperience = useCallback(() => {
    if (isExperienceRunning) {
      console.log('⚠️ Expérience déjà en cours');
      return;
    }

    console.log('🚀 DÉMARRAGE DE L\'EXPÉRIENCE CINÉMATOGRAPHIQUE');
    setIsExperienceRunning(true);
    openCurtains();
  }, [isExperienceRunning, openCurtains]);

  // 🛑 ARRÊT D'URGENCE
  const stopExperience = useCallback(() => {
    console.log('🛑 Arrêt d\'urgence de l\'expérience');
    
    // Nettoyer les timers
    if (phaseTimerRef.current) {
      clearTimeout(phaseTimerRef.current);
      phaseTimerRef.current = null;
    }

    // Réinitialiser l'état
    setIsExperienceRunning(false);
    updateState('idle');
    currentPhaseIndexRef.current = 0;
  }, [updateState]);

  // 🧹 Nettoyage au démontage
  React.useEffect(() => {
    return () => {
      if (phaseTimerRef.current) {
        clearTimeout(phaseTimerRef.current);
      }
    };
  }, []);

  return (
    <div ref={containerRef} className="relative w-full h-full overflow-hidden">
      {/* 🎭 VOLETS CINÉMATOGRAPHIQUES */}
      <div
        ref={leftCurtainRef}
        className="fixed top-0 left-0 w-1/2 h-full bg-black z-50 pointer-events-none"
        style={{ transform: 'translateX(0%)' }}
      />
      <div
        ref={rightCurtainRef}
        className="fixed top-0 right-0 w-1/2 h-full bg-black z-50 pointer-events-none"
        style={{ transform: 'translateX(0%)' }}
      />

      {/* 🎮 BOUTON DE CONTRÔLE PRINCIPAL */}
      {currentState === 'idle' && (
        <div className="fixed top-4 left-4 z-40">
          <button
            onClick={startExperience}
            disabled={isExperienceRunning}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50"
          >
            🎬 Démarrer l'expérience
          </button>
        </div>
      )}

      {/* 🛑 BOUTON D'ARRÊT D'URGENCE */}
      {isExperienceRunning && currentState !== 'complete' && (
        <div className="fixed top-4 right-4 z-40">
          <button
            onClick={stopExperience}
            className="px-4 py-2 bg-red-600 text-white font-semibold rounded-lg shadow-lg hover:bg-red-700 transition-all duration-300"
          >
            🛑 Arrêter
          </button>
        </div>
      )}

      {/* 📊 INDICATEUR DE PHASE ACTUELLE */}
      {isExperienceRunning && currentState === 'running' && (
        <div className="fixed bottom-4 left-4 z-40 bg-black/50 text-white px-4 py-2 rounded-lg">
          <div className="text-sm font-medium">
            Phase: {currentPhase} ({currentPhaseIndexRef.current + 1}/4)
          </div>
        </div>
      )}

      {/* 🎨 CONTENU DE L'APPLICATION */}
      <div className="relative w-full h-full">
        {children}
      </div>
    </div>
  );
};

export default CinemaController;
export type { CinemaControllerProps };
