import React, { useRef, useImperativeHandle, forwardRef, useCallback } from 'react';

// 🌅☀️🌇🌙 GESTIONNAIRE DU CYCLE AUTOMATIQUE 4 PHASES
// Positions astronomiques exactes selon les spécifications CISCO

export type AutoCyclePhase = 'aube' | 'midi' | 'coucher' | 'nuit';

export interface AutoCycleManagerRef {
  startCycle: () => void;
  stopCycle: () => void;
  pauseCycle: () => void;
  resumeCycle: () => void;
  getCurrentPhase: () => AutoCyclePhase;
  getProgress: () => number;
}

interface AutoCycleManagerProps {
  onPhaseChange: (phase: AutoCyclePhase, phaseIndex: number) => void;
  onCycleComplete: () => void;
  onProgress?: (progress: number, phaseProgress: number) => void;
  cycleDuration?: number; // En millisecondes (défaut: 8 minutes)
}

// 🌅☀️🌇🌙 POSITIONS ASTRONOMIQUES EXACTES (CISCO CRITICAL)
export const ASTRONOMICAL_POSITIONS = {
  aube: { 
    // 🌅 AUBE : Soleil sous l'horizon, lune disparaît, étoiles s'estompent
    soleil: { 
      startAngle: -40, // Complètement sous l'horizon
      endAngle: -10,   // Commence à apparaître
      startOpacity: 0, 
      endOpacity: 0.3 
    },
    lune: { 
      startOpacity: 0.3, // Visible en début d'aube
      endOpacity: 0      // Disparaît complètement
    },
    etoiles: { 
      startDensity: 'high',   // Encore visibles
      endDensity: 'medium'    // Commencent à disparaître
    },
    nuages: {
      filter: 'brightness(0.7) saturate(1.2) contrast(1.2) hue-rotate(8deg) sepia(0.1)'
    },
    duration: 120000, // 2 minutes
    emoji: '🌅',
    name: 'Aube'
  },
  
  midi: { 
    // ☀️ MIDI : Soleil au zénith, lune invisible, étoiles invisibles
    soleil: { 
      startAngle: -10, // Apparition
      endAngle: 90,    // ZÉNITH ABSOLU (position maximale)
      startOpacity: 0.3, 
      endOpacity: 1.0    // Pleine luminosité
    },
    lune: { 
      startOpacity: 0, // Invisible complet
      endOpacity: 0    // Reste invisible
    },
    etoiles: { 
      startDensity: 'medium', // Disparition progressive
      endDensity: 'none'      // INVISIBLES (jour complet)
    },
    nuages: {
      filter: 'brightness(1.2) saturate(1.0) contrast(1.0) hue-rotate(0deg)'
    },
    duration: 120000, // 2 minutes
    emoji: '☀️',
    name: 'Midi'
  },
  
  coucher: { 
    // 🌇 COUCHER : Soleil descend, lune apparaît, étoiles commencent
    soleil: { 
      startAngle: 90,  // Zénith
      endAngle: 170,   // Descente vers horizon opposé
      startOpacity: 1.0, 
      endOpacity: 0.3    // Diminution progressive
    },
    lune: { 
      startOpacity: 0,   // Invisible en début
      endOpacity: 0.3    // APPARITION PROGRESSIVE (côté opposé)
    },
    etoiles: { 
      startDensity: 'none',   // Invisibles en début
      endDensity: 'medium'    // SCINTILLEMENT COMMENCE
    },
    nuages: {
      filter: 'brightness(0.9) saturate(1.4) contrast(1.1) hue-rotate(25deg) sepia(0.2)'
    },
    duration: 120000, // 2 minutes
    emoji: '🌇',
    name: 'Coucher'
  },
  
  nuit: { 
    // 🌙 NUIT : Soleil invisible, lune visible, étoiles scintillantes
    soleil: { 
      startAngle: 170, // Horizon
      endAngle: 200,   // Complètement sous l'horizon
      startOpacity: 0.3, 
      endOpacity: 0      // INVISIBLE COMPLET
    },
    lune: { 
      startOpacity: 0.3, // Apparition
      endOpacity: 1.0    // VISIBLE COMPLET (position nocturne)
    },
    etoiles: { 
      startDensity: 'medium', // Scintillement
      endDensity: 'high'      // DENSITÉ MAXIMALE
    },
    nuages: {
      filter: 'brightness(0.4) saturate(0.8) contrast(1.0) hue-rotate(0deg)'
    },
    duration: 120000, // 2 minutes
    emoji: '🌙',
    name: 'Nuit'
  }
};

const AutoCycleManager = forwardRef<AutoCycleManagerRef, AutoCycleManagerProps>(({
  onPhaseChange,
  onCycleComplete,
  onProgress,
  cycleDuration = 480000 // 8 minutes par défaut
}, ref) => {

  // 🔄 État du cycle
  const currentPhaseRef = useRef<AutoCyclePhase>('aube');
  const currentPhaseIndexRef = useRef(0);
  const isRunningRef = useRef(false);
  const isPausedRef = useRef(false);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);

  // ⏱️ Références pour les timers
  const phaseTimerRef = useRef<NodeJS.Timeout | null>(null);
  const progressTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 🌅 Séquence des phases
  const phases: AutoCyclePhase[] = ['aube', 'midi', 'coucher', 'nuit'];

  // 📊 Calcul du progrès
  const calculateProgress = useCallback(() => {
    if (!isRunningRef.current) return 0;

    const now = Date.now();
    const elapsed = now - startTimeRef.current - pausedTimeRef.current;
    const totalProgress = Math.min(elapsed / cycleDuration, 1);
    
    // Progrès de la phase actuelle
    const phaseStartTime = (currentPhaseIndexRef.current * cycleDuration) / 4;
    const phaseDuration = cycleDuration / 4;
    const phaseElapsed = elapsed - phaseStartTime;
    const phaseProgress = Math.min(Math.max(phaseElapsed / phaseDuration, 0), 1);

    onProgress?.(totalProgress, phaseProgress);
    return totalProgress;
  }, [cycleDuration, onProgress]);

  // 🔄 Exécution d'une phase
  const executePhase = useCallback((phaseIndex: number) => {
    if (phaseIndex >= phases.length) {
      console.log('🎬 AutoCycleManager: Cycle terminé');
      stopCycle();
      onCycleComplete();
      return;
    }

    const phase = phases[phaseIndex];
    const phaseConfig = ASTRONOMICAL_POSITIONS[phase];
    
    console.log(`🌅 AutoCycleManager: Phase ${phaseIndex + 1}/4 - ${phaseConfig.name} ${phaseConfig.emoji} (${phaseConfig.duration/1000}s)`);
    
    currentPhaseRef.current = phase;
    currentPhaseIndexRef.current = phaseIndex;
    
    onPhaseChange(phase, phaseIndex);

    // Timer pour la phase suivante
    phaseTimerRef.current = setTimeout(() => {
      if (isRunningRef.current && !isPausedRef.current) {
        executePhase(phaseIndex + 1);
      }
    }, phaseConfig.duration);

  }, [phases, onPhaseChange, onCycleComplete]);

  // 🚀 Démarrage du cycle
  const startCycle = useCallback(() => {
    if (isRunningRef.current) {
      console.log('⚠️ AutoCycleManager: Cycle déjà en cours');
      return;
    }

    console.log('🚀 AutoCycleManager: Démarrage du cycle automatique (8 minutes)');
    
    isRunningRef.current = true;
    isPausedRef.current = false;
    startTimeRef.current = Date.now();
    pausedTimeRef.current = 0;
    currentPhaseIndexRef.current = 0;

    // Démarrer le suivi du progrès
    progressTimerRef.current = setInterval(calculateProgress, 100);

    // Démarrer la première phase
    executePhase(0);
  }, [executePhase, calculateProgress]);

  // 🛑 Arrêt du cycle
  const stopCycle = useCallback(() => {
    console.log('🛑 AutoCycleManager: Arrêt du cycle');
    
    isRunningRef.current = false;
    isPausedRef.current = false;

    // Nettoyer les timers
    if (phaseTimerRef.current) {
      clearTimeout(phaseTimerRef.current);
      phaseTimerRef.current = null;
    }
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }

    // Réinitialiser
    currentPhaseIndexRef.current = 0;
    currentPhaseRef.current = 'aube';
  }, []);

  // ⏸️ Pause du cycle
  const pauseCycle = useCallback(() => {
    if (!isRunningRef.current || isPausedRef.current) return;

    console.log('⏸️ AutoCycleManager: Pause du cycle');
    isPausedRef.current = true;
    pausedTimeRef.current += Date.now() - startTimeRef.current;

    // Arrêter les timers
    if (phaseTimerRef.current) {
      clearTimeout(phaseTimerRef.current);
      phaseTimerRef.current = null;
    }
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }
  }, []);

  // ▶️ Reprise du cycle
  const resumeCycle = useCallback(() => {
    if (!isRunningRef.current || !isPausedRef.current) return;

    console.log('▶️ AutoCycleManager: Reprise du cycle');
    isPausedRef.current = false;
    startTimeRef.current = Date.now();

    // Reprendre le suivi du progrès
    progressTimerRef.current = setInterval(calculateProgress, 100);

    // Reprendre la phase actuelle
    executePhase(currentPhaseIndexRef.current);
  }, [executePhase, calculateProgress]);

  // 📊 Getters
  const getCurrentPhase = useCallback(() => currentPhaseRef.current, []);
  const getProgress = useCallback(() => calculateProgress(), [calculateProgress]);

  // 🎯 Exposer les méthodes
  useImperativeHandle(ref, () => ({
    startCycle,
    stopCycle,
    pauseCycle,
    resumeCycle,
    getCurrentPhase,
    getProgress
  }));

  // 🧹 Nettoyage au démontage
  React.useEffect(() => {
    return () => {
      if (phaseTimerRef.current) {
        clearTimeout(phaseTimerRef.current);
      }
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
      }
    };
  }, []);

  // Ce composant ne rend rien (logique pure)
  return null;
});

AutoCycleManager.displayName = 'AutoCycleManager';

export default AutoCycleManager;
export { ASTRONOMICAL_POSITIONS };
