// 🎨 CISCO: VRAIES PALETTES DE COULEURS EXTRAITES DES CAPTURES D'ÉCRAN
// ✨ Source: ContextEngineering/Tasks/Cisco.md
// 📅 Créé: 2025-01-10
// 🔧 Utilisation: Remplacer les couleurs pastels par ces vraies couleurs naturelles

export const VRAIES_PALETTES_CISCO = {
  // 🌌 === NUIT PROFONDE === 🌌
  night: {
    primary: '#020a10',    // Horizon - Bleu nuit très sombre
    secondary: '#03213c',  // Milieu - Bleu nuit intermédiaire  
    tertiary: '#05315b',   // Zénith - Bleu nuit plus clair
    // Couleurs supplémentaires disponibles:
    // #04528a, #1a83bb, #6694b5
    gradient: 'linear-gradient(to top, #020a10 0%, #03213c 60%, #05315b 100%)'
  },

  // 🌅 === AUBE === 🌅  
  dawn: {
    primary: '#fec5b9',    // Horizon - Rose pêche doux
    secondary: '#dc8998',  // Milieu - Rose plus intense
    tertiary: '#787fa1',   // Zénith - Bleu gris lavande
    // Couleurs supplémentaires disponibles:
    // #718ec4, #f1dcd9
    gradient: 'linear-gradient(to top, #fec5b9 0%, #dc8998 50%, #787fa1 100%)'
  },

  // 🌤️ === LEVER DE SOLEIL === 🌤️
  sunrise: {
    primary: '#fcd68b',    // Horizon - Jaune doré éclatant
    secondary: '#b5ae8f',  // Milieu - Beige doré
    tertiary: '#517a8b',   // Zénith - Bleu brumeux
    // Couleurs supplémentaires disponibles:
    // #0c1915, #ffe683
    gradient: 'linear-gradient(to top, #fcd68b 0%, #b5ae8f 45%, #517a8b 100%)'
  },

  // ☀️ === MATIN === ☀️
  morning: {
    primary: '#ffd08c',    // Horizon - Orange pâle matinal
    secondary: '#c6c9ce',  // Milieu - Gris clair
    tertiary: '#688ab0',   // Zénith - Bleu poussiéreux
    // Couleurs supplémentaires disponibles:
    // #254351, #d2b7a4
    gradient: 'linear-gradient(to top, #ffd08c 0%, #c6c9ce 50%, #688ab0 100%)'
  },

  // 🕛 === ZÉNITH (12H00) === 🕛
  midday: {
    primary: '#e1ecf2',    // Horizon - Bleu très clair
    secondary: '#b6cbed',  // Milieu - Bleu clair
    tertiary: '#73a6e0',   // Zénith - Bleu pur intense
    // Couleurs supplémentaires disponibles:
    // #d2dcf4
    gradient: 'linear-gradient(to top, #e1ecf2 0%, #b6cbed 60%, #73a6e0 100%)'
  },

  // 🕒 === APRÈS-MIDI === 🕒
  afternoon: {
    primary: '#eef0f6',    // Horizon - Blanc bleuté très clair
    secondary: '#cfdaec',  // Milieu - Bleu très pâle
    tertiary: '#759cd8',   // Zénith - Bleu doux
    // Couleurs supplémentaires disponibles:
    // #9cb9d1
    gradient: 'linear-gradient(to top, #eef0f6 0%, #cfdaec 60%, #759cd8 100%)'
  },

  // 🌇 === COUCHER DE SOLEIL === 🌇
  sunset: {
    primary: '#E38664',    // Horizon - Orange chaud du soleil
    secondary: '#7E627B',  // Milieu - Violet rosé
    tertiary: '#5BA0BF',   // Zénith - Bleu du ciel
    // Couleurs supplémentaires disponibles:
    // #012442, #FFA566
    gradient: 'linear-gradient(to top, #E38664 0%, #7E627B 40%, #5BA0BF 100%)'
  },

  // 🌆 === CRÉPUSCULE === 🌆
  dusk: {
    primary: '#E8D9BE',    // Horizon - Beige chaud (Luz Alegre)
    secondary: '#B06A76',  // Milieu - Rose poussiéreux (Princesa)
    tertiary: '#2A3B5A',   // Zénith - Bleu sombre (Azul Colonial)
    // Couleurs supplémentaires disponibles:
    // #B16D4F (Disparado)
    gradient: 'linear-gradient(to top, #E8D9BE 0%, #B06A76 55%, #2A3B5A 100%)'
  }
};

// 🔧 CISCO: FONCTION UTILITAIRE - Récupérer une palette par mode
export const getVraiePalette = (mode: keyof typeof VRAIES_PALETTES_CISCO) => {
  return VRAIES_PALETTES_CISCO[mode];
};

// 🔧 CISCO: FONCTION UTILITAIRE - Récupérer le dégradé CSS complet
export const getVraiDegradeCss = (mode: keyof typeof VRAIES_PALETTES_CISCO) => {
  return VRAIES_PALETTES_CISCO[mode].gradient;
};

// 📝 CISCO: NOTES D'UTILISATION
// 1. Remplacer BACKGROUND_MODES dans DynamicBackground.tsx par VRAIES_PALETTES_CISCO
// 2. Utiliser getVraiePalette() pour récupérer les couleurs d'un mode
// 3. Utiliser getVraiDegradeCss() pour les dégradés CSS complets
// 4. Les couleurs sont extraites des vraies captures d'écran de ciels naturels
// 5. Transitions progressives et linéaires comme spécifié dans cisco.md
