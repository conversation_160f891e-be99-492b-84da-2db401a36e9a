
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
# ContextEngineering\Tasks\Cisco.md
# ContextEngineering\TechnicalNotes\journal-technique.md


# Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.


- Très important, attention dans le plan d'élaboration, donc de la refactorisation pour faire plus simple au niveau du code et de l'animation. Surtout n'oubliez pas la position du soleil au lever, au zénith, et ainsi qu'après l'inverse, le coucher du soleil et ensuite vient la lune. Il ne faut pas oublier. 



- - N'oubliez jamais la règle d'or. Il y a trois éléments essentiels à se rappeler. Dans l'animation principale, ce sont les dégradés qui représentent le ciel, les nuages qui doivent avoir le bon éclairage par rapport au mode si c'est le matin, à midi ou le soir, ou la nuit. Il faut que les nuages soient éclairés par cohérence, par mode. C'est-à-dire que la nuit, les nuages sont plus sombres et en pleine journée, ils sont normaux, ils ont l'éclairage, ils sont blancs. Le paysage aussi, même chose, le paysage, il doit être, en fait, il faut créer un éclairage qui suive une cohérence. Par exemple, le matin, quand le soleil se lève, il y a un type d'éclairage sur le paysage, sur les nuages, puis il y a les dégradés. Tout ça doit être cohérent, tout doit être progressif, tout doit être logique avec par exemple le lever du soleil. Quand le soleil se lève, le paysage va s'éclaircir progressivement, les nuages aussi, et ainsi que les dégradés en arrière-plan vont changer progressivement. Voilà la logique, il faut faire ça pour tous les modes. 


- Pour résumer en bref, conclusion, c'est très simple. Le matin, c'est une lumière douce et qui va au lever du soleil. Donc la lumière, on démarre du sombre et progressivement on va vers une lumière douce pour que tout soit cohérent. Nuages, arrière-plan dégradé, plus le background paysage, tout doit être cohérent. Ensuite, à midi, la lumière est au plus haut, c'est-à-dire tout est bien éclairé, donc à 100%. Et ensuite, quand on arrive le soir, au fur et à mesure qu'on s'approche du soir, la lumière fait l'inverse. C'est-à-dire qu'elle s'atténue en douceur, progressivement, tout doit être cohérent. Les nuages, le paysage s'assombrit tout doucement, jusqu'à arriver la nuit où là, l'éclairage est au plus bas. Voilà, c'est comme ça qu'il faut voir la chose. 

Attention tout de même, je vous rappelle que tout ce qui a été ancien, tout ce que vous avez développé, tout ce que vous n'avez pas besoin, attention, il faut être sûr à 100%, et bien vous l'enlevez. Si vous êtes sûr que ça ne nous sert plus du tout, vous l'enlevez. Attention, il ne faut laisser aucune trace d'ancien code, sinon ça va créer des conflits


-


- 




























