import { useState, useEffect, useRef, useCallback } from 'react';

// 🌅 TEMPORISATEUR DE JOURNÉE RÉVOLUTIONNAIRE V2
// Système complet avec persistance, navigation et synchronisation parfaite

// 🔧 CISCO: Types exportés pour utilisation dans le contexte
export type DayPhase = 'dawn' | 'sunrise' | 'morning' | 'midday' | 'afternoon' | 'sunset' | 'dusk' | 'night';
export type CycleStatus = 'stopped' | 'running' | 'paused';

export interface DayCycleState {
  currentPhase: DayPhase;
  phaseIndex: number;
  progress: number; // 0-1 progression dans le cycle complet
  phaseProgress: number; // 0-1 progression dans la phase actuelle
  elapsedTime: number;
  cycleDuration: number; // Durée totale du cycle en ms
  status: CycleStatus;
  lastUpdateTime: number; // 🔧 CISCO: Pour la persistance
  isManualMode: boolean; // 🔧 CISCO: Mode manuel pour contrôle de progression
  manualPhaseProgress: number | null; // 🔧 CISCO: Progression manuelle de la phase
}

export interface UseDayCycleTimerProps {
  onPhaseChange?: (phase: DayPhase, phaseIndex: number) => void;
  onCycleComplete?: () => void;
  onSoundChange?: (phase: DayPhase) => void; // 🔧 CISCO: Callback pour les sons
  initialCycleDuration?: number; // En minutes
  autoRestart?: boolean; // Redémarrer automatiquement après un cycle complet
  initialPhase?: DayPhase; // Phase de démarrage
  enablePersistence?: boolean; // 🔧 CISCO: Activer la persistance localStorage
  persistenceKey?: string; // 🔧 CISCO: Clé pour localStorage
}

// 🌍 PHASES DU CYCLE (dans l'ordre chronologique naturel d'une journée)
export const DAY_PHASES: DayPhase[] = [
  'dawn',       // 0 - Aube (début de journée)
  'sunrise',    // 1 - Lever du soleil
  'morning',    // 2 - Matin
  'midday',     // 3 - Midi (zénith)
  'afternoon',  // 4 - Après-midi
  'sunset',     // 5 - Coucher du soleil
  'dusk',       // 6 - Crépuscule
  'night'       // 7 - Nuit (fin de journée)
];

// 🎨 ÉMOJIS POUR CHAQUE PHASE
export const PHASE_EMOJIS: Record<DayPhase, string> = {
  dawn: '🌅',      // 0 - Aube (début)
  sunrise: '🌄',   // 1 - Lever du soleil
  morning: '🌞',   // 2 - Matin
  midday: '☀️',    // 3 - Midi (zénith)
  afternoon: '🌇', // 4 - Après-midi
  sunset: '🌆',    // 5 - Coucher du soleil
  dusk: '🌃',      // 6 - Crépuscule
  night: '🌌'      // 7 - Nuit (fin)
};

// 🔧 CISCO: FONCTIONS UTILITAIRES POUR LA PERSISTANCE
const DEFAULT_PERSISTENCE_KEY = 'dayCycleTimer_state';

const saveStateToStorage = (state: DayCycleState, key: string) => {
  try {
    localStorage.setItem(key, JSON.stringify(state));
  } catch (error) {
    console.warn('🔧 Impossible de sauvegarder l\'état du cycle:', error);
  }
};

const loadStateFromStorage = (key: string): Partial<DayCycleState> | null => {
  try {
    const saved = localStorage.getItem(key);
    if (saved) {
      const parsed = JSON.parse(saved);
      // Vérifier que l'état sauvegardé n'est pas trop ancien (plus de 24h)
      const now = Date.now();
      if (parsed.lastUpdateTime && (now - parsed.lastUpdateTime) < 24 * 60 * 60 * 1000) {
        return parsed;
      }
    }
  } catch (error) {
    console.warn('🔧 Impossible de charger l\'état du cycle:', error);
  }
  return null;
};

// 🎯 HOOK PRINCIPAL RÉVOLUTIONNAIRE V2
export const useDayCycleTimer = ({
  onPhaseChange,
  onCycleComplete,
  onSoundChange,
  initialCycleDuration = 8, // 8 minutes par défaut
  autoRestart = true,
  initialPhase = 'dawn', // Démarrage à l'aube (début naturel de journée)
  enablePersistence = true, // 🔧 CISCO: Persistance activée par défaut
  persistenceKey = DEFAULT_PERSISTENCE_KEY
}: UseDayCycleTimerProps = {}) => {

  // 🔧 CISCO: Calcul de l'index de la phase initiale
  const initialPhaseIndex = DAY_PHASES.indexOf(initialPhase);

  // 🔧 CISCO: Initialisation avec persistance
  const initializeState = useCallback((): DayCycleState => {
    if (enablePersistence) {
      const savedState = loadStateFromStorage(persistenceKey);
      if (savedState) {
        console.log('🔧 État du cycle restauré depuis localStorage:', savedState);
        return {
          currentPhase: savedState.currentPhase || initialPhase,
          phaseIndex: savedState.phaseIndex ?? initialPhaseIndex,
          progress: savedState.progress ?? (initialPhaseIndex / DAY_PHASES.length),
          phaseProgress: savedState.phaseProgress ?? 0,
          elapsedTime: savedState.elapsedTime ?? 0,
          cycleDuration: savedState.cycleDuration ?? (initialCycleDuration * 60 * 1000),
          status: 'stopped', // 🔧 CISCO: Toujours arrêté au chargement
          lastUpdateTime: Date.now(),
          isManualMode: false, // 🔧 CISCO: Toujours démarrer en mode automatique
          manualPhaseProgress: null
        };
      }
    }

    return {
      currentPhase: initialPhase,
      phaseIndex: initialPhaseIndex,
      progress: initialPhaseIndex / DAY_PHASES.length,
      phaseProgress: 0,
      elapsedTime: 0,
      cycleDuration: initialCycleDuration * 60 * 1000,
      status: 'stopped',
      lastUpdateTime: Date.now(),
      isManualMode: false, // 🔧 CISCO: Mode automatique par défaut
      manualPhaseProgress: null
    };
  }, [enablePersistence, persistenceKey, initialPhase, initialPhaseIndex, initialCycleDuration]);

  // 📊 État du cycle avec persistance
  const [state, setState] = useState<DayCycleState>(initializeState);

  // 🔄 Références pour le timer
  const intervalRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const lastPhaseRef = useRef<number>(state.phaseIndex);

  // 🔧 CISCO: Sauvegarde automatique de l'état
  const saveState = useCallback((newState: DayCycleState) => {
    if (enablePersistence) {
      saveStateToStorage(newState, persistenceKey);
    }
  }, [enablePersistence, persistenceKey]);

  // 🧮 CALCULS DES PHASES
  const getPhaseDuration = useCallback(() => {
    return state.cycleDuration / DAY_PHASES.length; // Durée égale pour chaque phase
  }, [state.cycleDuration]);

  const getCurrentPhaseFromTime = useCallback((elapsedTime: number) => {
    const phaseDuration = getPhaseDuration();
    const phaseIndex = Math.floor(elapsedTime / phaseDuration) % DAY_PHASES.length;
    const phaseProgress = (elapsedTime % phaseDuration) / phaseDuration;
    const totalProgress = (elapsedTime % state.cycleDuration) / state.cycleDuration;

    return {
      phaseIndex,
      currentPhase: DAY_PHASES[phaseIndex],
      phaseProgress,
      totalProgress
    };
  }, [getPhaseDuration, state.cycleDuration]);

  // 🔄 FONCTION DE MISE À JOUR DU TIMER AMÉLIORÉE
  const updateTimer = useCallback(() => {
    if (state.status !== 'running') return;

    const now = Date.now();
    const elapsedTime = now - startTimeRef.current;
    const phaseInfo = getCurrentPhaseFromTime(elapsedTime);

    const newState: DayCycleState = {
      ...state,
      elapsedTime,
      currentPhase: phaseInfo.currentPhase,
      phaseIndex: phaseInfo.phaseIndex,
      progress: phaseInfo.totalProgress,
      phaseProgress: phaseInfo.phaseProgress,
      lastUpdateTime: now
    };

    setState(newState);
    saveState(newState); // 🔧 CISCO: Sauvegarde automatique

    // 🎯 DÉTECTION CHANGEMENT DE PHASE
    if (phaseInfo.phaseIndex !== lastPhaseRef.current) {
      console.log(`🌅 CHANGEMENT DE PHASE: ${DAY_PHASES[lastPhaseRef.current]} → ${phaseInfo.currentPhase} (${phaseInfo.phaseIndex + 1}/8)`);

      // 🔧 CISCO: Callbacks pour phase et sons
      if (onPhaseChange) {
        onPhaseChange(phaseInfo.currentPhase, phaseInfo.phaseIndex);
      }
      if (onSoundChange) {
        onSoundChange(phaseInfo.currentPhase);
      }

      lastPhaseRef.current = phaseInfo.phaseIndex;
    }

    // 🔄 DÉTECTION FIN DE CYCLE
    if (elapsedTime >= state.cycleDuration) {
      console.log('🎉 CYCLE COMPLET TERMINÉ !');

      if (onCycleComplete) {
        onCycleComplete();
      }

      if (autoRestart) {
        // Redémarrer automatiquement
        console.log('🔄 REDÉMARRAGE AUTOMATIQUE DU CYCLE');
        startTimeRef.current = now;
        lastPhaseRef.current = 0;
      } else {
        // Arrêter le cycle
        stop();
      }
    }
  }, [state, getCurrentPhaseFromTime, onPhaseChange, onCycleComplete, onSoundChange, autoRestart, saveState]);

  // ⚡ DÉMARRAGE DU TIMER
  useEffect(() => {
    if (state.status === 'running') {
      intervalRef.current = window.setInterval(updateTimer, 100); // Mise à jour toutes les 100ms
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [state.status, updateTimer]);

  // 🎮 CONTRÔLES DU CYCLE

  // ▶️ DÉMARRER
  const start = useCallback(() => {
    console.log('🚀 DÉMARRAGE DU CYCLE DE JOURNÉE');
    startTimeRef.current = Date.now() - state.elapsedTime;
    lastPhaseRef.current = state.phaseIndex;

    const newState = {
      ...state,
      status: 'running' as CycleStatus,
      lastUpdateTime: Date.now()
    };
    setState(newState);
    saveState(newState);
  }, [state, saveState]);

  // ⏸️ PAUSE
  const pause = useCallback(() => {
    console.log('⏸️ PAUSE DU CYCLE');
    pausedTimeRef.current = Date.now();

    const newState = {
      ...state,
      status: 'paused' as CycleStatus,
      lastUpdateTime: Date.now()
    };
    setState(newState);
    saveState(newState);
  }, [state, saveState]);

  // ▶️ REPRENDRE
  const resume = useCallback(() => {
    console.log('▶️ REPRISE DU CYCLE');
    const pauseDuration = Date.now() - pausedTimeRef.current;
    startTimeRef.current += pauseDuration;

    const newState = {
      ...state,
      status: 'running' as CycleStatus,
      lastUpdateTime: Date.now()
    };
    setState(newState);
    saveState(newState);
  }, [state, saveState]);

  // ⏹️ ARRÊTER
  const stop = useCallback(() => {
    console.log('⏹️ ARRÊT DU CYCLE');

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    const newState = {
      ...state,
      status: 'stopped' as CycleStatus,
      lastUpdateTime: Date.now()
    };
    setState(newState);
    saveState(newState);
  }, [state, saveState]);

  // 🔄 RESET
  const reset = useCallback(() => {
    console.log('🔄 RESET DU CYCLE');

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    const newState: DayCycleState = {
      currentPhase: initialPhase,
      phaseIndex: initialPhaseIndex,
      progress: initialPhaseIndex / DAY_PHASES.length,
      phaseProgress: 0,
      elapsedTime: 0,
      cycleDuration: state.cycleDuration,
      status: 'stopped',
      lastUpdateTime: Date.now()
    };

    setState(newState);
    saveState(newState);
    lastPhaseRef.current = initialPhaseIndex;

    // 🔧 CISCO: Déclencher les callbacks pour synchroniser
    if (onPhaseChange) {
      onPhaseChange(initialPhase, initialPhaseIndex);
    }
    if (onSoundChange) {
      onSoundChange(initialPhase);
    }
  }, [initialPhase, initialPhaseIndex, state.cycleDuration, saveState, onPhaseChange, onSoundChange]);

  // ⚙️ CHANGER LA DURÉE DU CYCLE
  const setCycleDuration = useCallback((durationMinutes: number) => {
    const newDurationMs = durationMinutes * 60 * 1000;
    console.log(`⚙️ NOUVELLE DURÉE DE CYCLE: ${durationMinutes} minutes`);

    const newState = {
      ...state,
      cycleDuration: newDurationMs,
      lastUpdateTime: Date.now()
    };
    setState(newState);
    saveState(newState);
  }, [state, saveState]);

  // 🎛️ CISCO: CONTRÔLE MANUEL DE LA PROGRESSION DE PHASE
  const setPhaseProgress = useCallback((progress: number) => {
    // Valider la progression (0-1)
    const clampedProgress = Math.max(0, Math.min(1, progress));
    console.log(`🎛️ CONTRÔLE MANUEL PROGRESSION PHASE: ${(clampedProgress * 100).toFixed(1)}%`);

    const phaseDuration = getPhaseDuration();
    const phaseStartTime = state.phaseIndex * phaseDuration;
    const targetTime = phaseStartTime + (clampedProgress * phaseDuration);

    // 🔧 CISCO: Passer en mode manuel et calculer la nouvelle progression
    const newPhaseProgress = clampedProgress;
    const newTotalProgress = (state.phaseIndex + newPhaseProgress) / DAY_PHASES.length;

    // 🔧 CISCO: Si on atteint 100% de la phase, passer à la phase suivante
    let newPhaseIndex = state.phaseIndex;
    let newCurrentPhase = state.currentPhase;
    let finalPhaseProgress = newPhaseProgress;

    if (clampedProgress >= 1.0 && state.phaseIndex < DAY_PHASES.length - 1) {
      // Passer à la phase suivante
      newPhaseIndex = state.phaseIndex + 1;
      newCurrentPhase = DAY_PHASES[newPhaseIndex];
      finalPhaseProgress = 0;
      console.log(`➡️ PASSAGE AUTOMATIQUE À LA PHASE SUIVANTE: ${newCurrentPhase}`);
    } else if (clampedProgress <= 0.0 && state.phaseIndex > 0) {
      // Revenir à la phase précédente
      newPhaseIndex = state.phaseIndex - 1;
      newCurrentPhase = DAY_PHASES[newPhaseIndex];
      finalPhaseProgress = 1.0;
      console.log(`⬅️ RETOUR AUTOMATIQUE À LA PHASE PRÉCÉDENTE: ${newCurrentPhase}`);
    }

    const newState: DayCycleState = {
      ...state,
      currentPhase: newCurrentPhase,
      phaseIndex: newPhaseIndex,
      progress: (newPhaseIndex + finalPhaseProgress) / DAY_PHASES.length,
      phaseProgress: finalPhaseProgress,
      elapsedTime: newPhaseIndex * phaseDuration + (finalPhaseProgress * phaseDuration),
      isManualMode: true,
      manualPhaseProgress: finalPhaseProgress,
      lastUpdateTime: Date.now()
    };

    setState(newState);
    saveState(newState);

    // 🔧 CISCO: Ajuster la référence de temps si le timer est en cours
    if (state.status === 'running') {
      startTimeRef.current = Date.now() - newState.elapsedTime;
    }

    // 🔊 CISCO: Déclencher les callbacks de changement de phase si nécessaire
    if (newCurrentPhase !== state.currentPhase) {
      if (onPhaseChange) {
        onPhaseChange(newCurrentPhase, newPhaseIndex);
      }
      if (onSoundChange) {
        onSoundChange(newCurrentPhase);
      }
    }
  }, [state, getPhaseDuration, saveState, onPhaseChange, onSoundChange]);

  // 🎯 ALLER DIRECTEMENT À UNE PHASE - CISCO: Correction navigation sans redémarrage automatique
  const goToPhase = useCallback((targetPhase: DayPhase) => {
    const phaseIndex = DAY_PHASES.indexOf(targetPhase);
    if (phaseIndex === -1) return;

    const phaseDuration = getPhaseDuration();
    const targetTime = phaseIndex * phaseDuration;

    console.log(`🎯 SAUT VERS LA PHASE: ${targetPhase} (${phaseIndex + 1}/8) - Timer: ${state.status}`);

    // 🔧 CISCO: CORRECTION CRITIQUE - Ne modifier startTimeRef que si le timer est en cours
    if (state.status === 'running') {
      // Timer en cours : ajuster la référence de temps pour continuer depuis la nouvelle phase
      startTimeRef.current = Date.now() - targetTime;
      console.log(`⏰ Timer en cours - Ajustement de la référence de temps`);
    } else {
      // Timer arrêté/pausé : ne pas modifier startTimeRef pour éviter le redémarrage automatique
      console.log(`⏸️ Timer arrêté/pausé - Pas d'ajustement de temps (évite redémarrage automatique)`);
    }

    lastPhaseRef.current = phaseIndex;

    const newState: DayCycleState = {
      ...state,
      currentPhase: targetPhase,
      phaseIndex,
      elapsedTime: targetTime,
      progress: targetTime / state.cycleDuration,
      phaseProgress: 0,
      lastUpdateTime: Date.now()
    };

    setState(newState);
    saveState(newState);

    // 🔧 CISCO: Déclencher tous les callbacks
    if (onPhaseChange) {
      onPhaseChange(targetPhase, phaseIndex);
    }
    if (onSoundChange) {
      onSoundChange(targetPhase);
    }
  }, [state, getPhaseDuration, saveState, onPhaseChange, onSoundChange]);

  // 🔧 CISCO: NOUVELLES FONCTIONS DE NAVIGATION

  // ⬅️ PHASE PRÉCÉDENTE
  const goToPreviousPhase = useCallback(() => {
    const currentIndex = state.phaseIndex;
    const previousIndex = currentIndex === 0 ? DAY_PHASES.length - 1 : currentIndex - 1;
    const previousPhase = DAY_PHASES[previousIndex];

    console.log(`⬅️ PHASE PRÉCÉDENTE: ${state.currentPhase} → ${previousPhase}`);
    goToPhase(previousPhase);
  }, [state.phaseIndex, state.currentPhase, goToPhase]);

  // ➡️ PHASE SUIVANTE
  const goToNextPhase = useCallback(() => {
    const currentIndex = state.phaseIndex;
    const nextIndex = (currentIndex + 1) % DAY_PHASES.length;
    const nextPhase = DAY_PHASES[nextIndex];

    console.log(`➡️ PHASE SUIVANTE: ${state.currentPhase} → ${nextPhase}`);
    goToPhase(nextPhase);
  }, [state.phaseIndex, state.currentPhase, goToPhase]);

  // 🧹 NETTOYAGE
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // 📤 RETOUR DES DONNÉES ET CONTRÔLES RÉVOLUTIONNAIRE V2
  return {
    // 📊 État actuel
    currentPhase: state.currentPhase,
    phaseIndex: state.phaseIndex,
    progress: state.progress,
    phaseProgress: state.phaseProgress,
    elapsedTime: state.elapsedTime,
    cycleDuration: state.cycleDuration,
    status: state.status,
    lastUpdateTime: state.lastUpdateTime,

    // 🎨 Utilitaires
    currentPhaseEmoji: PHASE_EMOJIS[state.currentPhase],
    phaseName: state.currentPhase,
    phaseNumber: state.phaseIndex + 1,
    totalPhases: DAY_PHASES.length,
    phaseDuration: getPhaseDuration(),

    // 🎮 Contrôles principaux
    start,
    pause,
    resume,
    stop,
    reset,
    setCycleDuration,

    // 🔧 CISCO: Nouveaux contrôles de navigation
    goToPhase,
    goToPreviousPhase,
    goToNextPhase,
    setPhaseProgress, // 🎛️ CISCO: Contrôle manuel de la progression de phase

    // 📋 Données de référence
    allPhases: DAY_PHASES,
    phaseEmojis: PHASE_EMOJIS,

    // 🔧 État technique
    isRunning: state.status === 'running',
    isPaused: state.status === 'paused',
    isStopped: state.status === 'stopped',

    // 🔧 CISCO: Nouvelles propriétés utilitaires
    canGoPrevious: true, // Toujours possible (cycle circulaire)
    canGoNext: true, // Toujours possible (cycle circulaire)
    previousPhase: DAY_PHASES[state.phaseIndex === 0 ? DAY_PHASES.length - 1 : state.phaseIndex - 1],
    nextPhase: DAY_PHASES[(state.phaseIndex + 1) % DAY_PHASES.length],

    // 🎛️ CISCO: Contrôle manuel
    isManualMode: state.isManualMode,
    manualPhaseProgress: state.manualPhaseProgress,

    // 🔧 CISCO: Informations de persistance
    persistenceEnabled: enablePersistence,
    persistenceKey
  };
};

export default useDayCycleTimer;
