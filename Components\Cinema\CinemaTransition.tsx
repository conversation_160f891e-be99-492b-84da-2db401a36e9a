import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { gsap } from 'gsap';

// 🎭 COMPOSANT VOLETS CINÉMATOGRAPHIQUES
// Gère l'ouverture/fermeture des volets avec animations GSAP fluides

export interface CinemaTransitionRef {
  openCurtains: () => Promise<void>;
  closeCurtains: () => Promise<void>;
  resetCurtains: () => void;
}

interface CinemaTransitionProps {
  onOpenComplete?: () => void;
  onCloseComplete?: () => void;
  curtainColor?: string;
  animationDuration?: number;
}

const CinemaTransition = forwardRef<CinemaTransitionRef, CinemaTransitionProps>(({
  onOpenComplete,
  onCloseComplete,
  curtainColor = '#000000',
  animationDuration = 2
}, ref) => {
  
  // 🎭 Références pour les volets
  const leftCurtainRef = useRef<HTMLDivElement>(null);
  const rightCurtainRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  // 🎬 OUVERTURE DES VOLETS (glissement horizontal)
  const openCurtains = (): Promise<void> => {
    return new Promise((resolve) => {
      console.log('🎭 CinemaTransition: Ouverture des volets...');
      
      // Tuer toute animation en cours
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✅ CinemaTransition: Volets ouverts');
          onOpenComplete?.();
          resolve();
        }
      });

      // Animation simultanée des deux volets
      timelineRef.current
        .to(leftCurtainRef.current, {
          x: '-100%', // Glisse vers la gauche
          duration: animationDuration,
          ease: 'power2.inOut'
        }, 0)
        .to(rightCurtainRef.current, {
          x: '100%', // Glisse vers la droite
          duration: animationDuration,
          ease: 'power2.inOut'
        }, 0);
    });
  };

  // 🎬 FERMETURE DES VOLETS (retour à la position initiale)
  const closeCurtains = (): Promise<void> => {
    return new Promise((resolve) => {
      console.log('🎭 CinemaTransition: Fermeture des volets...');
      
      // Tuer toute animation en cours
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✅ CinemaTransition: Volets fermés');
          onCloseComplete?.();
          resolve();
        }
      });

      // Retour des volets à leur position initiale
      timelineRef.current
        .to(leftCurtainRef.current, {
          x: '0%', // Retour au centre
          duration: animationDuration,
          ease: 'power2.inOut'
        }, 0)
        .to(rightCurtainRef.current, {
          x: '0%', // Retour au centre
          duration: animationDuration,
          ease: 'power2.inOut'
        }, 0);
    });
  };

  // 🔄 RÉINITIALISATION DES VOLETS (position fermée immédiate)
  const resetCurtains = () => {
    console.log('🔄 CinemaTransition: Réinitialisation des volets');
    
    // Tuer toute animation en cours
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Position immédiate
    gsap.set([leftCurtainRef.current, rightCurtainRef.current], {
      x: '0%'
    });
  };

  // 🎯 Exposer les méthodes via useImperativeHandle
  useImperativeHandle(ref, () => ({
    openCurtains,
    closeCurtains,
    resetCurtains
  }));

  // 🧹 Nettoyage au démontage
  React.useEffect(() => {
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
    };
  }, []);

  return (
    <>
      {/* 🎭 VOLET GAUCHE */}
      <div
        ref={leftCurtainRef}
        className="fixed top-0 left-0 w-1/2 h-full pointer-events-none"
        style={{
          backgroundColor: curtainColor,
          zIndex: 9999, // Au-dessus de tout
          transform: 'translateX(0%)'
        }}
      >
        {/* 🎨 Effet de bordure pour le réalisme */}
        <div 
          className="absolute top-0 right-0 w-1 h-full bg-gradient-to-r from-transparent to-black/20"
        />
      </div>

      {/* 🎭 VOLET DROIT */}
      <div
        ref={rightCurtainRef}
        className="fixed top-0 right-0 w-1/2 h-full pointer-events-none"
        style={{
          backgroundColor: curtainColor,
          zIndex: 9999, // Au-dessus de tout
          transform: 'translateX(0%)'
        }}
      >
        {/* 🎨 Effet de bordure pour le réalisme */}
        <div 
          className="absolute top-0 left-0 w-1 h-full bg-gradient-to-l from-transparent to-black/20"
        />
      </div>
    </>
  );
});

CinemaTransition.displayName = 'CinemaTransition';

export default CinemaTransition;
