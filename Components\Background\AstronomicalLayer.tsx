import React, { useEffect, useRef } from 'react';
import NewStars from './NewStars'; // 🔧 CISCO: Nouveau composant étoiles simplifié
import MoonAnimation from '../UI/MoonAnimation';
import SunriseAnimation, { SunriseAnimationRef } from './SunriseAnimation'; // 🔧 CISCO: Soleil intégré
import { useDayCycleOptional } from '../Context/DayCycleContext'; // 🔧 CISCO: Connexion au temporisateur

// Interface pour les props du composant
interface AstronomicalLayerProps {
  // Mode du ciel pour contrôler la visibilité des étoiles
  skyMode?: 'night' | 'dawn' | 'sunrise' | 'morning' | 'midday' | 'afternoon' | 'sunset' | 'dusk';
}

const AstronomicalLayer: React.FC<AstronomicalLayerProps> = ({ skyMode = 'night' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sunriseAnimationRef = useRef<SunriseAnimationRef>(null); // 🔧 CISCO: Référence pour le soleil

  // 🔧 CISCO: Connexion au temporisateur de journée - MAÎTRE ABSOLU
  const dayCycleContext = useDayCycleOptional();
  const currentPhase = dayCycleContext?.currentPhase || skyMode;
  const isTimerRunning = dayCycleContext?.isRunning || false;








  // 🔧 CISCO: Exposition initiale des fonctions du soleil au montage
  useEffect(() => {
    const exposeFunction = () => {
      if (sunriseAnimationRef.current) {
        (window as any).triggerSunriseAnimation = sunriseAnimationRef.current.triggerSunrise;
        (window as any).triggerMorningAnimation = sunriseAnimationRef.current.triggerMorning;
        (window as any).triggerMiddayAnimation = sunriseAnimationRef.current.triggerMidday;
        (window as any).triggerAfternoonAnimation = sunriseAnimationRef.current.triggerAfternoon;
        (window as any).triggerSunsetAnimation = sunriseAnimationRef.current.triggerSunset;
        (window as any).triggerDawnAnimation = sunriseAnimationRef.current.triggerDawn;
        (window as any).triggerDuskAnimation = sunriseAnimationRef.current.triggerDusk;
        (window as any).triggerNightSunAnimation = sunriseAnimationRef.current.triggerNight;
        console.log('☀️ Fonctions soleil exposées globalement depuis AstronomicalLayer');
        return true;
      }
      return false;
    };

    // Essayer d'exposer immédiatement, sinon réessayer
    if (!exposeFunction()) {
      const interval = setInterval(() => {
        if (exposeFunction()) {
          clearInterval(interval);
          // 🔧 CISCO: Déclencher automatiquement le soleil à l'aube au démarrage - SYNCHRONISÉ
          setTimeout(() => {
            if (sunriseAnimationRef.current) {
              console.log('☀️ Déclenchement automatique du soleil à l\'aube (dawn) au démarrage');
              sunriseAnimationRef.current.triggerDawn();
            }
          }, 200);
        }
      }, 100);

      // Nettoyer l'interval après 5s max
      setTimeout(() => clearInterval(interval), 5000);
    } else {
      // 🔧 CISCO: Si exposition immédiate réussie, déclencher le soleil à l'aube - SYNCHRONISÉ
      setTimeout(() => {
        if (sunriseAnimationRef.current) {
          console.log('☀️ Déclenchement automatique du soleil à l\'aube (dawn) au démarrage');
          sunriseAnimationRef.current.triggerDawn();
        }
      }, 200);
    }
  }, []); // 🔧 CISCO: Une seule fois au montage

  // 🔧 CISCO: CONTRÔLE AUTOMATIQUE DU SOLEIL PAR LE TEMPORISATEUR - MAÎTRE ABSOLU
  useEffect(() => {
    // Si le temporisateur n'est pas en cours, le soleil et la lune restent fixes
    if (!isTimerRunning) {
      console.log(`⏸️ Temporisateur arrêté - Soleil et lune FIXES en position ${currentPhase}`);
      return;
    }

    // Si le temporisateur est en cours, déclencher l'animation selon la phase
    if (sunriseAnimationRef.current) {
      console.log(`☀️ TEMPORISATEUR ACTIF - Déclenchement animation soleil pour phase: ${currentPhase}`);

      switch (currentPhase) {
        case 'night':
          sunriseAnimationRef.current.triggerNight();
          break;
        case 'dawn':
          sunriseAnimationRef.current.triggerDawn();
          break;
        case 'sunrise':
          sunriseAnimationRef.current.triggerSunrise();
          break;
        case 'morning':
          sunriseAnimationRef.current.triggerMorning();
          break;
        case 'midday':
          sunriseAnimationRef.current.triggerMidday();
          break;
        case 'afternoon':
          sunriseAnimationRef.current.triggerAfternoon();
          break;
        case 'sunset':
          sunriseAnimationRef.current.triggerSunset();
          break;
        case 'dusk':
          sunriseAnimationRef.current.triggerDusk();
          break;
        default:
          console.warn(`⚠️ Phase inconnue: ${currentPhase}`);
          break;
      }
    }
  }, [currentPhase, isTimerRunning]);

  // 🔧 CISCO: Suppression du double système d'étoiles - NewStars s'occupe de tout
  useEffect(() => {
    console.log(`🌌 AstronomicalLayer: Mode ${currentPhase} - Délégation à NewStars`);
  }, [currentPhase]);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 pointer-events-none"
      style={{
        zIndex: 8 // 🔧 CISCO: Couche astronomique (étoiles z-7 + lune z-8) - VERROUILLÉ
      }}
    >
      {/* 🌟 CISCO: Étoiles contrôlées par le temporisateur - MAÎTRE ABSOLU */}
      <NewStars skyMode={currentPhase} density="high" />

      {/* ☀️ CISCO: Soleil intégré dans la couche astronomique - DERRIÈRE les nuages */}
      <SunriseAnimation
        ref={sunriseAnimationRef}
        isVisible={true}
      />

      {/* 🌙 CISCO: Lune contrôlée par le temporisateur - MAÎTRE ABSOLU */}
      <MoonAnimation
        isNightMode={currentPhase === 'night' && isTimerRunning}
        currentMode={currentPhase}
      />
    </div>
  );
};

export default AstronomicalLayer;
